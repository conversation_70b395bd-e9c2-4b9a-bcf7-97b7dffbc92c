/* pages/home/<USER>/

/* 重要操作提示 */
.operation-notice {
  position: sticky;
  top: 0;
  z-index: 100;
  margin: 0 -32rpx 20rpx -32rpx;
  background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.operation-notice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  animation: notice-pulse 2s ease-in-out infinite;
}

.operation-notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  animation: notice-shake 1s ease-in-out infinite;
}

.operation-notice-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  line-height: 1.4;
  text-align: center;
  flex: 1;
}

@keyframes notice-pulse {
  0%, 100% {
    background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
  }
  50% {
    background: linear-gradient(90deg, #ff5252, #ff7979);
  }
}

@keyframes notice-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2rpx); }
  75% { transform: translateX(2rpx); }
}

/* 页面容器 */
.page-container {
  min-height: calc(100vh + 200rpx);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  padding: 0 32rpx 200rpx 32rpx;
  box-sizing: border-box;
}

/* 欢迎区域 */
.welcome-section {
  padding: 60rpx 0 40rpx 0;
  text-align: center;
}

.welcome-header {
  margin-bottom: 16rpx;
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 8rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.welcome-desc {
  margin-top: 16rpx;
}

.desc-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 重要提示区域 */
.notice-section {
  margin-bottom: 40rpx;
}

.notice-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
}

.notice-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.notice-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
}

.notice-content {
  
}

.notice-item {
  margin-bottom: 16rpx;
}

.notice-item:last-child {
  margin-bottom: 0;
}

.notice-text {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.6;
}

.highlight {
  color: #ff6b6b;
  font-weight: 600;
}

/* 功能导航区域 */
.function-section {
  margin-bottom: 40rpx;
}

.section-title {
  margin-bottom: 24rpx;
  text-align: center;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 8rpx;
}

.title-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.function-grid {
  display: flex;
  gap: 24rpx;
}

.function-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.function-item:active {
  transform: scale(0.95);
}

.function-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.function-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.function-desc {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
}

/* 提示词区域 */
.prompts-section {
  margin-bottom: 40rpx;
}

.prompts-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.prompt-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.prompt-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.prompt-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.prompt-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.copy-icon {
  font-size: 24rpx;
  color: #667eea;
}

.prompt-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 快速开始区域 */
.quick-start-section {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
  padding-bottom: 40rpx;
}

.start-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 64rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 700;
  border: none;
  box-shadow: 
    0 12rpx 40rpx rgba(102, 126, 234, 0.4),
    0 4rpx 12rpx rgba(118, 75, 162, 0.3);
  transition: all 0.3s ease;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.start-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 32rpx;
}

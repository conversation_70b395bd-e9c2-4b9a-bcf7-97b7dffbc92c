<!--pages/process/process.wxml - 现代化设计版本-->
<scroll-view class="page-container" scroll-y="true" enhanced="true" show-scrollbar="false">
  <!-- 渐变背景装饰 -->
  <view class="bg-decoration">
    <view class="bg-circle bg-circle-1"></view>
    <view class="bg-circle bg-circle-2"></view>
    <view class="bg-circle bg-circle-3"></view>
  </view>

  <!-- 现代化标题区域 -->
  <view class="hero-section">
    <view class="hero-content">
      <text class="hero-title">智能创作工坊</text>
      <text class="hero-subtitle">{{mode === 'image' ? '让智能技术为你的图片注入魔法' : '用文字描绘你的想象世界'}}</text>
    </view>
    <view class="hero-icon">
      <text class="icon-text">{{mode === 'image' ? '🎨' : '✨'}}</text>
    </view>
  </view>

  <!-- 现代化模式切换 -->
  <view class="mode-selector">
    <view class="mode-tabs">
      <view
        class="mode-tab {{mode === 'image' ? 'active' : ''}}"
        data-mode="image"
        bindtap="switchMode"
      >
        <view class="tab-icon">🖼️</view>
        <text class="tab-text">图片处理</text>
        <view class="tab-indicator"></view>
      </view>
      <view
        class="mode-tab {{mode === 'text' ? 'active' : ''}}"
        data-mode="text"
        bindtap="switchMode"
      >
        <view class="tab-icon">🎭</view>
        <text class="tab-text">文字生成</text>
        <view class="tab-indicator"></view>
      </view>
    </view>
  </view>

  <!-- 现代化图片选择区域 -->
  <view class="content-section" wx:if="{{mode === 'image'}}">
    <view class="section-card image-upload-card">
      <view class="card-header">
        <text class="card-title">选择图片</text>
        <text class="card-subtitle">支持JPG、PNG格式，最大10MB</text>
      </view>
      <view class="image-upload-area" bindtap="selectImage">
        <!-- 图片处理中状态 -->
        <view wx:if="{{imageProcessing}}" class="upload-placeholder processing">
          <view class="upload-icon processing-icon">⏳</view>
          <text class="upload-text">处理图片中...</text>
          <text class="upload-hint">请稍候</text>
        </view>

        <!-- 已选择的图片 -->
        <image
          wx:elif="{{selectedImageBase64}}"
          src="{{selectedImageBase64}}"
          class="uploaded-image"
          mode="aspectFit"
        ></image>

        <!-- 默认上传提示 -->
        <view wx:else class="upload-placeholder">
          <view class="upload-icon">📸</view>
          <text class="upload-text">点击上传图片</text>
          <text class="upload-hint">或从相册选择</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 图片创作提示区域 -->
  <view class="content-section" wx:if="{{mode === 'text'}}">
    <view class="section-card prompt-card">
      <view class="card-header">
        <text class="card-title">图片创作</text>
        <text class="card-subtitle">用文字描述，为你创作独特的艺术作品</text>
      </view>
      <view class="features">
        <view class="feature-item">
          <text class="feature-icon">🎨</text>
          <text class="feature-text">多种艺术风格</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">⚡</text>
          <text class="feature-text">快速生成</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🌟</text>
          <text class="feature-text">高质量输出</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 现代化配置选项 -->
  <view class="content-section">
    <view class="section-card config-card">
      <view class="card-header">
        <text class="card-title">创作配置</text>
        <text class="card-subtitle">选择处理类型和输出参数</text>
      </view>

      <view class="config-grid">
        <!-- 处理类型选择 -->
        <view class="config-item radio-group-item">
          <view class="config-header">
            <text class="label-icon">🎯</text>
            <text class="label-text">处理类型</text>
          </view>
          <view class="radio-group">
            <view
              wx:for="{{processTypes}}"
              wx:key="id"
              class="radio-option {{selectedProcessType === item.id ? 'selected' : ''}}"
              data-type="{{item.id}}"
              bindtap="selectProcessType"
            >
              <view class="radio-circle">
                <view class="radio-dot" wx:if="{{selectedProcessType === item.id}}"></view>
              </view>
              <view class="radio-content">
                <view class="radio-header">
                  <text class="radio-icon">{{item.icon}}</text>
                  <text class="radio-name">{{item.name}}</text>
                  <text class="radio-cost">{{item.pointsCost}}积分</text>
                </view>
                <text class="radio-desc">{{item.description}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 尺寸比例选择 -->
        <view class="config-item radio-group-item">
          <view class="config-header">
            <text class="label-icon">📐</text>
            <text class="label-text">输出比例</text>
          </view>
          <view class="radio-group compact">
            <view
              wx:for="{{aspectRatios}}"
              wx:key="id"
              class="radio-option compact {{selectedAspectRatio === item.value ? 'selected' : ''}}"
              data-ratio="{{item.value}}"
              bindtap="selectAspectRatio"
            >
              <view class="radio-circle">
                <view class="radio-dot" wx:if="{{selectedAspectRatio === item.value}}"></view>
              </view>
              <text class="radio-name">{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 现代化输入区域 -->
  <view class="content-section">
    <!-- 文字输入（图片模式） -->
    <view class="section-card input-card" wx:if="{{mode === 'image'}}">
      <view class="card-header">
        <text class="card-title">添加文字</text>
        <text class="card-subtitle">在图片上叠加文字内容（可选）</text>
      </view>
      <view class="input-wrapper">
        <input
          class="modern-input"
          placeholder="输入要添加的文字内容..."
          value="{{textContent}}"
          bindinput="onTextContentInput"
          maxlength="200"
        />
        <view class="input-actions">
          <view
            wx:if="{{textContent.length > 0}}"
            class="clear-btn"
            bindtap="clearTextContent"
          >
            <text class="clear-icon">✕</text>
          </view>
          <view class="input-counter">{{textContent.length}}/200</view>
        </view>
      </view>
    </view>

    <!-- 主要描述输入 -->
    <view class="section-card input-card main-input-card">
      <view class="card-header">
        <text class="card-title">
          {{mode === 'image' ? '🎨 处理要求' : '✨ 创作描述'}}
        </text>
        <text class="card-subtitle">
          {{mode === 'image' ? '详细描述你希望如何处理这张图片' : '用文字描绘你想要的画面，越详细越好'}}
        </text>
      </view>
      <view class="textarea-wrapper">
        <textarea
          class="modern-textarea"
          placeholder="{{mode === 'image' ? '例如：让这张图片更加鲜艳明亮，增加温暖的色调...' : '例如：一只可爱的橘猫坐在樱花树下，粉色花瓣飘落，温暖的阳光透过树叶洒下，动漫风格...'}}"
          value="{{prompt}}"
          bindinput="onPromptInput"
          maxlength="1000"
        ></textarea>
        <view class="textarea-actions">
          <view class="textarea-left-actions">
            <view
              wx:if="{{prompt.length > 0}}"
              class="clear-btn"
              bindtap="clearPrompt"
            >
              <text class="clear-icon">✕</text>
            </view>
          </view>
          <view class="textarea-right-actions">
            <view
              wx:if="{{prompt.length >= 5}}"
              class="polish-btn {{polishing ? 'polishing' : ''}}"
              bindtap="polishPrompt"
            >
              <text class="polish-icon" wx:if="{{!polishing}}">✨</text>
              <view class="polish-loading" wx:if="{{polishing}}">
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
              </view>
            </view>
            <view class="textarea-counter {{prompt.length < 5 && prompt.length > 0 ? 'counter-warning' : ''}}">{{prompt.length}}/1000</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 现代化底部操作区域 -->
  <view class="action-section">
    <!-- 友好提示 -->
    <view class="friendly-tip">
      <text class="tip-icon">💡</text>
      <text class="tip-text">如遇页面无法点击，请点击小程序右上角三个点，重新进入小程序</text>
    </view>

    <view class="points-display">
      <view class="points-item current-points">
        <text class="points-icon">💎</text>
        <view class="points-info">
          <text class="points-label">当前积分</text>
          <text class="points-value">{{userPoints}}</text>
        </view>
      </view>
      <view class="points-divider"></view>
      <view class="points-item cost-points">
        <text class="points-icon">⚡</text>
        <view class="points-info">
          <text class="points-label">本次消耗</text>
          <text class="points-value cost">{{currentProcessType.pointsCost}}</text>
        </view>
      </view>
    </view>

    <button
      class="create-btn {{loading || submitting ? 'loading' : ''}} {{prompt.length < 5 ? 'disabled' : ''}}"
      bindtap="submitProcess"
      disabled="{{loading || submitting || prompt.length < 5}}"
    >
      <view class="btn-content">
        <text class="btn-icon" wx:if="{{!loading}}">{{mode === 'image' ? '🎨' : '✨'}}</text>
        <text class="btn-text">
          {{loading || submitting ? (mode === 'image' ? '魔法处理中...' : '智能创作中...') :
            (!isLoggedIn ? (mode === 'image' ? '登录开始魔法处理' : '登录开始智能创作') :
            (userPoints < currentProcessType.pointsCost ? '积分不足' :
            (mode === 'image' ? '开始魔法处理' : '开始智能创作')))}}
        </text>
      </view>
      <view class="btn-loading" wx:if="{{loading || submitting}}">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
    </button>
  </view>
</scroll-view>

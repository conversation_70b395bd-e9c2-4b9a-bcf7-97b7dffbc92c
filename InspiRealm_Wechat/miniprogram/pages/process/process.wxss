/* pages/process/process.wxss - Instagram创作工具风格 */

/* Instagram风格创作页面 */
.page-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  padding-bottom: env(safe-area-inset-bottom);
  overflow: visible;
}

/* 背景装饰元素 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.bg-circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
    opacity: 1;
  }
}

/* 英雄区域 */
.hero-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 80rpx 40rpx 60rpx;
  position: relative;
  z-index: 1;
}

.hero-content {
  flex: 1;
}

.hero-title {
  font-size: 56rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  animation: slideInLeft 0.8s ease-out;
}

.hero-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  animation: slideInLeft 0.8s ease-out 0.2s both;
}

.hero-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  animation: slideInRight 0.8s ease-out 0.4s both;
}

.icon-text {
  font-size: 60rpx;
  animation: bounce 2s infinite;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

/* 现代化模式选择器 */
.mode-selector {
  padding: 0 40rpx 40rpx;
  position: relative;
  z-index: 1;
}

.mode-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 8rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.mode-tab {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.mode-tab.active {
  background: white;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  transition: transform 0.3s ease;
}

.mode-tab.active .tab-icon {
  transform: scale(1.1);
}

.tab-text {
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.mode-tab.active .tab-text {
  color: #333;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.mode-tab.active .tab-indicator {
  width: 40rpx;
}

/* 内容区域 */
.content-section {
  padding: 0 40rpx 20rpx;
  position: relative;
  z-index: 1;
  overflow: visible;
}

/* 现代化卡片样式 */
.section-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s ease-out;
  overflow: visible;
}

.card-header {
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: block;
}

.card-subtitle {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图片上传区域 */
.image-upload-card {
  border: 2rpx dashed rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.image-upload-card:hover {
  border-color: rgba(102, 126, 234, 0.6);
  box-shadow: 0 16rpx 48rpx rgba(102, 126, 234, 0.15);
}

.image-upload-area {
  min-height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.image-upload-area:active {
  transform: scale(0.99);
}

.uploaded-image {
  width: 100%;
  height: 400rpx;
  object-fit: contain;
  background: #f8f9fa;
}

.upload-placeholder {
  text-align: center;
  padding: 60rpx 40rpx;
}

.upload-placeholder.processing {
  background: rgba(102, 126, 234, 0.1);
}

.processing-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  animation: pulse 2s infinite;
}

.upload-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
  display: block;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

/* 图片创作提示卡片 */
.prompt-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
}

.prompt-card .card-title {
  color: #4c51bf;
  font-weight: 700;
}

.prompt-card .card-subtitle {
  color: #553c9a;
  background: rgba(255, 255, 255, 0.95);
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.features {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.feature-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 配置区域 */
.config-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 255, 0.98) 100%);
  overflow: visible;
}

.config-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  overflow: visible;
}

.config-item {
  position: relative;
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  z-index: 100;
  overflow: visible;
}

.config-item:nth-child(1) {
  z-index: 1000;
}

.config-item:nth-child(2) {
  z-index: 999;
}

.config-item:nth-child(3) {
  z-index: 998;
}

.config-item:nth-child(4) {
  z-index: 997;
}

/* 单选按钮组样式 */
.radio-group-item {
  padding: 24rpx;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.config-header .label-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.config-header .label-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

/* 比例提示切换按钮 */
.ratio-tip-toggle {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 20rpx;
  border: 1rpx solid rgba(33, 150, 243, 0.2);
}

.tip-toggle-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.tip-toggle-text {
  font-size: 22rpx;
  color: #1976d2;
  font-weight: 500;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.config-item .radio-group.compact {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8rpx;
  justify-content: space-between;
}

.radio-option {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.radio-option.compact {
  flex: 1;
  min-width: 100rpx;
  max-width: 130rpx;
  align-items: center;
  padding: 14rpx 8rpx;
  justify-content: center;
  min-height: 56rpx;
  flex-direction: row;
}

.radio-option:hover {
  background: #e3f2fd;
  border-color: #667eea;
}

.radio-option.selected {
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
  border-color: #667eea;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
}

.radio-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 3rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.radio-option.selected .radio-circle {
  border-color: #667eea;
  background: #667eea;
}

.radio-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: white;
}

.radio-content {
  flex: 1;
  min-width: 0;
}

.radio-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.radio-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.radio-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.radio-cost {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.radio-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.radio-option.compact .radio-name {
  font-size: 24rpx;
  text-align: center;
  font-weight: 500;
  margin-left: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.radio-option.compact .radio-circle {
  width: 28rpx;
  height: 28rpx;
  margin-right: 0;
  flex-shrink: 0;
}

.radio-option.compact .radio-dot {
  width: 10rpx;
  height: 10rpx;
}

.config-item:hover {
  border-color: #667eea;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.1);
}

.config-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  cursor: pointer;
}

.selector-content {
  flex: 1;
}

.selector-label {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.label-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.label-text {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
}

.selector-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.value-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.value-cost {
  font-size: 22rpx;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.selector-value-simple {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.selector-arrow {
  margin-left: 16rpx;
  font-size: 20rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.selector-arrow.rotate {
  transform: rotate(180deg);
}



/* 输入区域 */
.input-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 255, 0.98) 100%);
}

.main-input-card {
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.1);
}

.input-wrapper,
.textarea-wrapper {
  position: relative;
}

.input-actions,
.textarea-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12rpx;
}

.textarea-left-actions,
.textarea-right-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.clear-btn:active {
  transform: scale(0.9);
  background: rgba(255, 107, 107, 0.2);
}

.clear-icon {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: 600;
}

/* 润色按钮样式 */
.polish-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: rgba(102, 126, 234, 0.15);
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 1;
  z-index: 10;
}

.polish-btn:active {
  transform: scale(0.9);
  background: rgba(102, 126, 234, 0.2);
}

.polish-btn.polishing {
  background: rgba(102, 126, 234, 0.15);
}

.polish-icon {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
}

/* 润色加载动画 */
.polish-loading {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.polish-loading .loading-dot {
  width: 4rpx;
  height: 4rpx;
  background: #667eea;
  border-radius: 50%;
  animation: polish-loading 1.4s infinite ease-in-out both;
}

.polish-loading .loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.polish-loading .loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes polish-loading {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.modern-input {
  width: calc(100% - 4rpx);
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  box-sizing: border-box;
  margin: 0 2rpx;
}

.modern-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  outline: none;
}

.modern-textarea {
  width: calc(100% - 4rpx);
  min-height: 240rpx;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  transition: all 0.3s ease;
  resize: none;
  box-sizing: border-box;
  margin: 0 2rpx;
}

.modern-textarea:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  outline: none;
}

.input-counter,
.textarea-counter {
  font-size: 20rpx;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.counter-warning {
  color: #ff6b6b !important;
  background: rgba(255, 107, 107, 0.1) !important;
}

/* 底部操作区域 */
.action-section {
  padding: 0 40rpx 20rpx;
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));
  position: relative;
  z-index: 1;
}

/* 友好提示 */
.friendly-tip {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 0 40rpx 32rpx 40rpx;
  border-left: 8rpx solid #2196f3;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.2);
  border: 2rpx solid rgba(33, 150, 243, 0.2);
}

.tip-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(33, 150, 243, 0.3));
}

.tip-text {
  font-size: 28rpx;
  color: #1565c0;
  line-height: 1.6;
  font-weight: 600;
  flex: 1;
  text-shadow: 0 1rpx 2rpx rgba(21, 101, 192, 0.1);
}

/* 积分显示 */
.points-display {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.points-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.points-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.points-info {
  display: flex;
  flex-direction: column;
}

.points-label {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.points-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.points-value.cost {
  color: #ff6b6b;
}

.points-divider {
  width: 2rpx;
  height: 40rpx;
  background: #e8e8e8;
  margin: 0 20rpx;
}

/* 创作按钮 */
.create-btn {
  width: 100%;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 28rpx;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.create-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.5);
}

.create-btn.loading {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.create-btn.disabled {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
  box-shadow: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  animation: sparkle 2s infinite;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  line-height: 1;
  text-align: center;
}

.btn-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 8rpx;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  background: white;
  border-radius: 50%;
  animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(180deg);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 按钮光效 */
.create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.create-btn:active::before {
  left: 100%;
}


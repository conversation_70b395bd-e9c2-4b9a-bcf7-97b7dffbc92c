// pages/process/process.ts
import { imageApi, userApi, errorHandler } from '../../utils/api'
import { imageUtils } from '../../utils/util'

interface ProcessType {
  id: string
  name: string
  description: string
  icon: string
  pointsCost: number
}

interface AspectRatio {
  id: string
  name: string
  value: string
}

interface Style {
  id: string
  name: string
  description: string
}

Page({
  data: {
    // 模式选择：'image' 图片处理模式，'text' 文字生成模式
    mode: 'image',

    // 当前选中的图片
    selectedImage: '',
    selectedImageBase64: '',

    // 图片处理类型
    imageProcessTypes: [
      { id: 'beautify', name: '图片美化', description: '自动美化图片，提升画质', icon: '✨', pointsCost: 10 },
      { id: 'style_transfer', name: '风格转换', description: '转换图片艺术风格', icon: '🎨', pointsCost: 10 },
      { id: 'cartoon', name: '卡通化', description: '将照片转为卡通风格', icon: '🎭', pointsCost: 10 },
      { id: 'enhance', name: '画质增强', description: '提升图片分辨率和清晰度', icon: '🔍', pointsCost: 10 }
    ] as ProcessType[],

    // 文字生成类型
    textGenerateTypes: [
      { id: 'realistic', name: '写实风格', description: '生成逼真的图片', icon: '📸', pointsCost: 10 },
      { id: 'artistic', name: '艺术风格', description: '生成艺术化图片', icon: '🎨', pointsCost: 10 },
      { id: 'anime', name: '动漫风格', description: '生成动漫风格图片', icon: '🎭', pointsCost: 10 },
      { id: 'fantasy', name: '奇幻风格', description: '生成奇幻主题图片', icon: '🌟', pointsCost: 10 }
    ] as ProcessType[],

    // 当前处理类型列表（根据模式动态切换）
    processTypes: [] as ProcessType[],
    selectedProcessType: 'beautify',

    // 尺寸比例
    aspectRatios: [
      { id: 'original', name: '原始比例', value: '' },
      { id: '1:1', name: '正方形', value: '1:1' },
      { id: '4:3', name: '标准', value: '4:3' },
      { id: '16:9', name: '宽屏', value: '16:9' },
      { id: '3:4', name: '竖屏', value: '3:4' }
    ] as AspectRatio[],
    selectedAspectRatio: '',

    // 风格选项
    styles: [
      { id: 'realistic', name: '写实', description: '保持真实感' },
      { id: 'artistic', name: '艺术', description: '艺术化处理' },
      { id: 'vintage', name: '复古', description: '复古风格' },
      { id: 'modern', name: '现代', description: '现代简约' }
    ] as Style[],
    selectedStyle: 'realistic',

    // 文字内容
    textContent: '',

    // 处理描述
    prompt: '',

    // UI状态
    loading: false,
    showStyles: false,
    imageProcessing: false,
    submitting: false, // 防抖状态
    polishing: false, // 润色状态

    // 用户状态
    isLoggedIn: false,
    userInfo: null,
    userPoints: 0,

    // 当前选中的处理类型信息
    currentProcessType: {} as ProcessType,

    // 当前选中的风格信息
    currentStyle: {} as Style
  },

  onLoad(options: any) {
    // 如果从其他页面传递了图片URL，直接使用
    if (options.imageUrl) {
      this.setData({
        selectedImage: decodeURIComponent(options.imageUrl)
      })
    }

    // 如果传递了处理类型，设置默认值
    if (options.processType) {
      this.setData({
        selectedProcessType: options.processType
      })
    }

    // 加载用户积分信息
    this.loadUserPoints()

    // 初始化处理类型列表和当前选项
    this.initProcessTypes()
    this.updateCurrentProcessType()
    this.updateCurrentStyle()

    // 立即渲染用户状态
    this.renderUserStatus()
  },

  onShow() {
    console.log('🔄 创作页面显示，立即渲染用户状态')

    // 更新底部导航栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }

    // 立即渲染用户状态
    this.renderUserStatus()
  },

  /**
   * 立即渲染用户状态
   */
  renderUserStatus() {
    console.log('🎨 开始渲染用户状态')

    // 立即从本地存储获取最新状态
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')

    console.log('📊 本地存储状态:', {
      hasToken: !!token,
      hasUserInfo: !!userInfo,
      userInfo: userInfo
    })

    if (token && userInfo) {
      // 用户已登录
      const availablePoints = (userInfo.pointsInfo && userInfo.pointsInfo.availablePoints) || userInfo.availablePoints || 0

      console.log('✅ 用户已登录，积分:', availablePoints)

      this.setData({
        isLoggedIn: true,
        userInfo: userInfo,
        userPoints: availablePoints
      })
    } else {
      // 用户未登录或已退出
      console.log('❌ 用户未登录或已退出')

      this.setData({
        isLoggedIn: false,
        userInfo: null,
        userPoints: 0
      })
    }

    console.log('🎨 用户状态渲染完成，当前状态:', {
      isLoggedIn: this.data.isLoggedIn,
      userPoints: this.data.userPoints
    })
  },

  /**
   * 异步检查登录状态并更新用户信息（保留原方法用于需要API验证的场景）
   */
  async checkLoginStatusAndUpdateUserInfo() {
    try {
      // 检查本地存储的登录状态
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('userInfo')

      console.log('🔐 检查本地登录状态:', {
        hasToken: !!token,
        hasUserInfo: !!userInfo,
        tokenValue: token ? token.substring(0, 10) + '...' : 'null',
        userInfoValue: userInfo ? userInfo.nickname || 'unknown' : 'null'
      })

      if (token && userInfo) {
        // 有token和用户信息，更新页面状态
        const availablePoints = (userInfo.pointsInfo && userInfo.pointsInfo.availablePoints) || userInfo.availablePoints || 0

        this.setData({
          isLoggedIn: true,
          userInfo: userInfo,
          userPoints: availablePoints
        })

        console.log('✅ 用户已登录，用户信息:', userInfo)
        console.log('✅ 积分信息:', {
          pointsInfo: userInfo.pointsInfo,
          availablePoints: userInfo.availablePoints,
          finalPoints: availablePoints
        })

        // 可选：验证token是否仍然有效（调用一个轻量级的API）
        try {
          const response = await userApi.getUserInfo()
          if (response.data) {
            // 更新最新的用户信息
            const latestUserInfo = response.data
            const latestPoints = (latestUserInfo.pointsInfo && latestUserInfo.pointsInfo.availablePoints) || latestUserInfo.availablePoints || 0

            this.setData({
              userInfo: latestUserInfo,
              userPoints: latestPoints
            })

            // 更新本地存储
            wx.setStorageSync('userInfo', latestUserInfo)
            console.log('🔄 用户信息已更新，最新积分:', latestPoints)
            console.log('🔄 最新用户信息:', latestUserInfo)
          }
        } catch (error) {
          console.log('⚠️ 验证token失败，可能需要重新登录:', error)
          // token可能已过期，清除本地状态
          this.setData({
            isLoggedIn: false,
            userInfo: null,
            userPoints: 0
          })
        }
      } else {
        // 没有登录信息
        console.log('❌ 用户未登录')
        this.setData({
          isLoggedIn: false,
          userInfo: null,
          userPoints: 0
        })
      }
    } catch (error) {
      console.error('❌ 检查登录状态失败:', error)
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        userPoints: 0
      })
    }
  },

  /**
   * 初始化处理类型列表
   */
  initProcessTypes() {
    const types = this.data.mode === 'image' ? this.data.imageProcessTypes : this.data.textGenerateTypes
    const defaultType = this.data.mode === 'image' ? 'beautify' : 'realistic'

    this.setData({
      processTypes: types,
      selectedProcessType: defaultType
    })
  },

  /**
   * 切换模式
   */
  switchMode(e: any) {
    const { mode } = e.currentTarget.dataset

    this.setData({
      mode,
      // 清空图片选择（切换到文字模式时）
      selectedImage: mode === 'text' ? '' : this.data.selectedImage,
      selectedImageBase64: mode === 'text' ? '' : this.data.selectedImageBase64,
      // 重置输入内容
      prompt: '',
      textContent: ''
    })

    // 重新初始化处理类型
    this.initProcessTypes()
    this.updateCurrentProcessType()
  },

  /**
   * 加载用户积分信息
   */
  async loadUserPoints() {
    try {
      const response = await userApi.getPointsInfo()
      this.setData({
        userPoints: response.data.availablePoints || 0
      })
    } catch (error) {
      console.error('加载积分信息失败:', error)
    }
  },

  /**
   * 选择图片
   */
  selectImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath

        // 保存原始路径用于后续处理，并设置处理状态
        this.setData({
          selectedImage: tempFilePath,
          imageProcessing: true
        })

        // 显示处理提示
        wx.showLoading({
          title: '处理图片中...',
          mask: true
        })

        // 将图片转换为base64用于显示和上传
        this.convertImageToBase64(tempFilePath)
      },
      fail: (error) => {
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 将图片转换为base64
   */
  async convertImageToBase64(filePath: string) {
    try {
      const base64Data = await imageUtils.convertToBase64(filePath)

      this.setData({
        selectedImageBase64: base64Data,
        imageProcessing: false
      })

      // 隐藏加载提示
      wx.hideLoading()

      // 显示成功提示
      wx.showToast({
        title: '图片选择成功',
        icon: 'success',
        duration: 1500
      })

    } catch (error) {
      // 重置处理状态
      this.setData({
        imageProcessing: false
      })

      // 隐藏加载提示
      wx.hideLoading()

      wx.showToast({
        title: '图片处理失败',
        icon: 'none'
      })
    }
  },



  /**
   * 更新当前处理类型信息
   */
  updateCurrentProcessType() {
    const currentType = this.data.processTypes.find(type => type.id === this.data.selectedProcessType) || this.data.processTypes[0]
    this.setData({
      currentProcessType: currentType
    })
  },

  /**
   * 更新当前风格信息
   */
  updateCurrentStyle() {
    const currentStyle = this.data.styles.find(style => style.id === this.data.selectedStyle) || this.data.styles[0]
    this.setData({
      currentStyle: currentStyle
    })
  },

  /**
   * 选择处理类型
   */
  selectProcessType(e: any) {
    const { type } = e.currentTarget.dataset
    this.setData({
      selectedProcessType: type
    })
    this.updateCurrentProcessType()
  },

  /**
   * 选择尺寸比例
   */
  selectAspectRatio(e: any) {
    const { ratio } = e.currentTarget.dataset
    this.setData({
      selectedAspectRatio: ratio
    })
  },

  /**
   * 选择风格
   */
  selectStyle(e: any) {
    const { style } = e.currentTarget.dataset
    this.setData({
      selectedStyle: style,
      showStyles: false
    })
    this.updateCurrentStyle()
  },



  toggleStyles() {
    this.setData({
      showStyles: !this.data.showStyles
    })
  },

  /**
   * 输入文字内容
   */
  onTextContentInput(e: any) {
    this.setData({
      textContent: e.detail.value
    })
  },

  /**
   * 输入处理描述
   */
  onPromptInput(e: any) {
    this.setData({
      prompt: e.detail.value
    })
  },

  /**
   * 清除文字内容
   */
  clearTextContent() {
    this.setData({
      textContent: ''
    })
  },

  /**
   * 清除处理描述
   */
  clearPrompt() {
    this.setData({
      prompt: ''
    })
  },

  /**
   * 润色提示词
   */
  async polishPrompt() {
    const prompt = this.data.prompt.trim()

    // 检查长度
    if (prompt.length < 5) {
      wx.showToast({
        title: '内容太短，无法润色',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 防止重复点击
    if (this.data.polishing) {
      return
    }

    this.setData({ polishing: true })

    try {
      console.log('开始润色提示词:', prompt)

      // 获取当前选中的图片（如果有的话）
      const imageBase64 = this.data.mode === 'image' ? this.data.selectedImageBase64 : null
      console.log('润色时是否包含图片:', !!imageBase64)

      const response = await imageApi.polishPrompt(prompt, imageBase64)

      if (response.success && response.data) {
        // 更新提示词
        this.setData({
          prompt: response.data
        })

        wx.showToast({
          title: '润色完成',
          icon: 'success',
          duration: 2000
        })

        console.log('润色完成:', response.data)
      } else {
        throw new Error(response.message || '润色失败')
      }

    } catch (error) {
      console.error('润色失败:', error)
      wx.showToast({
        title: '润色失败，请稍后重试',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({ polishing: false })
    }
  },

  /**
   * 提交处理任务
   */
  async submitProcess() {
    console.log('🎨 开始魔法处理按钮被点击')

    // 防抖检查：如果正在提交中，直接返回
    if (this.data.submitting) {
      console.log('⚠️ 正在提交中，忽略重复点击')
      wx.showToast({
        title: '请勿重复点击',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // 设置提交状态
    this.setData({ submitting: true })

    // 立即渲染最新的用户状态
    this.renderUserStatus()

    // 检查登录状态
    if (!this.data.isLoggedIn) {
      console.log('❌ 用户未登录，提示登录')
      this.setData({ submitting: false }) // 重置防抖状态
      wx.showModal({
        title: '需要登录',
        content: '魔法处理功能需要登录后使用，是否前往登录？',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        }
      })
      return
    }

    console.log('✅ 用户已登录，当前积分:', this.data.userPoints)

    // 根据模式进行不同的验证
    if (this.data.mode === 'image') {
      // 图片处理模式：需要图片和处理要求
      if (!this.data.selectedImageBase64) {
        this.setData({ submitting: false }) // 重置防抖状态
        wx.showToast({
          title: '请先选择图片',
          icon: 'none'
        })
        return
      }

      if (!this.data.prompt.trim()) {
        this.setData({ submitting: false }) // 重置防抖状态
        wx.showToast({
          title: '请输入处理要求',
          icon: 'none'
        })
        return
      }

      if (this.data.prompt.trim().length < 5) {
        this.setData({ submitting: false }) // 重置防抖状态
        wx.showToast({
          title: '处理要求至少需要5个字符',
          icon: 'none',
          duration: 2000
        })
        return
      }
    } else {
      // 文字生成模式：只需要描述
      if (!this.data.prompt.trim()) {
        this.setData({ submitting: false }) // 重置防抖状态
        wx.showToast({
          title: '请输入图片描述',
          icon: 'none'
        })
        return
      }

      if (this.data.prompt.trim().length < 5) {
        this.setData({ submitting: false }) // 重置防抖状态
        wx.showToast({
          title: '图片描述至少需要5个字符',
          icon: 'none',
          duration: 2000
        })
        return
      }
    }

    // 检查积分是否足够
    const pointsCost = this.data.currentProcessType.pointsCost || 5

    if (this.data.userPoints < pointsCost) {
      console.log('❌ 用户积分不足', { userPoints: this.data.userPoints, pointsCost })
      this.setData({ submitting: false }) // 重置防抖状态
      wx.showModal({
        title: '积分不足',
        content: `魔法处理需要${pointsCost}积分，您当前有${this.data.userPoints}积分。\n\n您可以通过完成任务或分享作品获得更多积分！`,
        confirmText: '查看任务',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 跳转到任务页面
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        }
      })
      return
    }

    this.setData({ loading: true })

    try {
      // 根据模式构建不同的请求参数
      let requestData: any = {
        prompt: this.data.prompt.trim(),
        processType: this.data.selectedProcessType,
        aspectRatio: this.data.selectedAspectRatio || undefined,
        style: this.data.selectedStyle
      }

      // 图片处理模式：添加图片和文字内容参数
      if (this.data.mode === 'image') {
        requestData.imageBase64 = this.data.selectedImageBase64
        if (this.data.textContent.trim()) {
          requestData.textContent = this.data.textContent.trim()
        }
      }
      // 文字生成模式：不需要额外参数，prompt已经包含了所有描述

      console.log('提交图片处理请求:', {
        ...requestData,
        imageBase64: '[BASE64_DATA]' // 不打印完整的base64数据
      })

      // 调用API提交任务
      const response = await imageApi.processImage(requestData)

      console.log('图片处理任务创建成功:', response.data)

      // 显示成功提示
      wx.showToast({
        title: '任务提交成功',
        icon: 'success'
      })

      // 延迟跳转到任务页面
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/profile/profile',
          success: () => {
            // 跳转到任务页面
            setTimeout(() => {
              wx.navigateTo({
                url: '/pages/my-tasks/my-tasks'
              })
            }, 500)
          }
        })
      }, 1500)

    } catch (error) {
      console.error('提交处理任务失败:', error)

      // 显示错误信息
      let errorMessage = '提交失败，请重试'
      if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = (error as any).message
      }

      wx.showModal({
        title: '提交失败',
        content: errorMessage,
        showCancel: false,
        confirmText: '确定'
      })
    } finally {
      this.setData({
        loading: false,
        submitting: false // 重置防抖状态
      })
    }
  }
})

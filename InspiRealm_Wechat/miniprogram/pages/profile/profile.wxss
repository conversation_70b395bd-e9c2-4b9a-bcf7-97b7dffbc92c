/* pages/profile/profile.wxss */

/* Instagram风格个人主页 */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-attachment: fixed;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999999;
  margin-top: 24rpx;
}

/* 主要内容 */
.main-content {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* Instagram风格用户信息区域 - 渐变玻璃效果 */
.user-section {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 24rpx 24rpx 32rpx 24rpx;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.user-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
  border-radius: 24rpx 24rpx 0 0;
}

/* Instagram风格全局动画 */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-section {
  animation: fadeInUp 0.6s ease forwards;
}

/* 已登录用户信息 */
.user-info {

}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 32rpx;
  background-color: #f0f0f0;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-basic {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 小型签到按钮 */
.checkin-mini {
  flex-shrink: 0;
}

.checkin-mini-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  border: none;
  border-radius: 32rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.checkin-mini-btn:not(.checked):active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.checkin-mini-btn.checked {
  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.checkin-mini-btn[disabled] {
  opacity: 0.7;
  transform: none !important;
}

.nickname {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.user-id {
  display: block;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.points-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.points-label {
  font-size: 26rpx;
  color: #666666;
}

.points-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #007aff;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background-color: #f8f9ff;
  border-radius: 12rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

/* 未登录状态 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
}

.login-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.login-title {
  font-size: 40rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.login-desc {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.login-btn {
  width: 400rpx;
  height: 88rpx;
  background-color: #007aff !important;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  font-size: 32rpx;
  color: #ffffff !important;
  font-weight: 500;
  line-height: 88rpx;
}

.login-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.login-btn::after {
  border: none;
}

/* 信息完善模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  box-sizing: border-box;
}

.modal-content {
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(248, 250, 255, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.3),
    0 8rpx 32rpx rgba(102, 126, 234, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  position: relative;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 38rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: 0.5rpx;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  transition: all 0.2s ease;
}

.modal-close:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

.close-icon {
  font-size: 28rpx;
  color: #666666;
  font-weight: 300;
}

/* 模态框内容 */
.modal-body {
  padding: 40rpx;
}

.avatar-section,
.nickname-section {
  margin-bottom: 40rpx;
}

.avatar-section:last-child,
.nickname-section:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.avatar-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx dashed #d0d7de;
  transition: all 0.2s ease;
  width: 100%;
}

.avatar-btn:active {
  transform: scale(0.98);
  background-color: #f1f3f4;
  border-color: #007aff;
}

.temp-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 16rpx;
  background-color: #e9ecef;
}

.avatar-tip {
  font-size: 24rpx;
  color: #666666;
}

.nickname-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  font-size: 30rpx;
  color: #333333;
  transition: all 0.2s ease;
}

.nickname-input:focus {
  border-color: #007aff;
  background-color: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx;
}

.cancel-btn,
.save-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #e0e0e0;
}

.cancel-btn:active {
  background-color: #eeeeee;
  transform: scale(0.98);
}

.save-btn {
  background-color: #007aff;
  color: #ffffff;
  border: none;
}

.save-btn:active {
  background-color: #0056cc;
  transform: scale(0.98);
}

.save-btn[disabled] {
  background-color: #cccccc;
  color: #999999;
  transform: none;
}

.cancel-btn::after,
.save-btn::after {
  border: none;
}

/* 功能菜单区域 */
.menu-section {
  margin-bottom: 32rpx;
}

/* 使用提示 */
.usage-tip {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%);
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin: 0 32rpx 32rpx 32rpx;
  border-left: 6rpx solid #2196f3;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.15);
}

.usage-tip .tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.usage-tip .tip-text {
  font-size: 26rpx;
  color: #1976d2;
  line-height: 1.5;
  font-weight: 500;
  flex: 1;
}

.section-title {
  font-size: 32rpx;
  font-weight: 800;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  padding: 0 32rpx;
  letter-spacing: 0.5rpx;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 32rpx;
  bottom: -8rpx;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 2rpx;
}

.menu-list {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  margin: 24rpx;
  position: relative;
}

.menu-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 100%;
  animation: gradient-shift 3s ease infinite;
  border-radius: 24rpx 24rpx 0 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9ff;
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.menu-info {
  flex: 1;
}

.menu-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.menu-desc {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}

.menu-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #cccccc;
  font-weight: 300;
}

/* 快捷操作区域 */
.action-section {
  margin-bottom: 32rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(248, 250, 255, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.15),
    0 2rpx 8rpx rgba(118, 75, 162, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.action-item:active {
  transform: scale(0.98);
  background-color: #f8f9ff;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

/* 登出按钮 */
.logout-section {
  margin-bottom: 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 50%, #ff4757 100%);
  border: none;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 8rpx 25rpx rgba(255, 107, 107, 0.4),
    0 4rpx 12rpx rgba(238, 90, 82, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.logout-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow:
    0 12rpx 30rpx rgba(255, 107, 107, 0.5),
    0 6rpx 15rpx rgba(238, 90, 82, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.logout-btn:active::before {
  left: 100%;
}

.logout-text {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 800;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

/* 页脚信息 */
.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 0;
  gap: 8rpx;
}

.version-text {
  font-size: 24rpx;
  color: #cccccc;
}

.copyright {
  font-size: 22rpx;
  color: #cccccc;
}


